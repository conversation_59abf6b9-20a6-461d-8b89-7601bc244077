# Project Architecture Documentation

## Technology Stack

### Core Technologies

- **Frontend Framework**: Vanilla JavaScript (ES6 modules) / Svelte / React
- **Build Tool**: Vite 5.4.1
- **Graphics Engine**: Phaser for 2D graphics rendering
- **Physics Engine**: Matter.js 0.20.0 for physics simulations
- **Audio Engine**:
  - Howler.js 2.2.4 for advanced audio management
  - CreateJS SoundJS for additional audio features
- **Animation**: Tween.js for smooth animations
- **Code Protection**: JavaScript obfuscation via vite-plugin-javascript-obfuscator

### Additional Libraries

- **WebSocket**: ws 8.18.0 for real-time communication

## Project Structure

```
Core-Games/
├── index.html                 # Main entry point
├── router.js                  # Client-side routing system
├── socket.js                  # WebSocket communication
├── uiManager.js              # UI state management
├── util.js                   # Utility functions
├── audio.js                  # Audio management
├── style.css                 # Global styles
├── vite.config.js            # Build configuration
├── package.json              # Dependencies and scripts
├── Games/                    # Individual game modules
│   ├── {ID}/
│   │   ├── index.html       # Game-specific HTML
│   │   ├── script.js        # Game logic
│   │   ├── style.css        # Game-specific styles
│   │   └── constants.js     # Game configuration
│   ├── htp/                 # How-to-play module
│   │   ├── htp.css          # How-to-play styles
│   │   └── htp.js           # How-to-play functionality
├── assets/                   # Game assets
│   ├── audio/               # Audio files
│   └── [images]             # Image assets
├── public/                   # Static assets for production
└── dist/                     # Built application
```

## Architecture Patterns

### 1. Modular Game Architecture

Each game is self-contained with its own:

- `index.html` - Game-specific HTML structure
- `script.js` - Game logic and initialization
- `style.css` - Game-specific styling
- `constants.js` - Game configuration and constants

### 2. Client-Side Routing

- Hash-based routing system (`router.js`)
- Dynamic script loading for games
- Manifest-based asset management for production builds
- URL parameter parsing for game configuration

### 3. State Management

- Global state management through `window` object
- UI state updates via `uiManager.js`
- Local storage for user preferences (sound, music)
- Real-time balance and game state synchronization

### 4. Communication Layer

- WebSocket integration for real-time features
- PostMessage API for iframe communication
- Base64 encoded URL parameters for secure data transfer
- Token-based authentication system

## Core Components

### Router System (`router.js`)

- Handles navigation between games and main menu
- Supports query parameters for game configuration
- Dynamic script injection for game modules
- Manifest integration for production builds

### UI Manager (`uiManager.js`)

- Centralized UI state management
- Animation queue system for smooth transitions
- Balance update animations
- Point drop effects

### Audio System (`audio.js`)

- Multi-format audio support
- Background music management
- Sound effect system
- User preference persistence
- Volume control and muting

### Utility Functions (`util.js`)

- Random number generation
- Base64 encoding/decoding for URL parameters
- Mobile device detection
- Cross-platform compatibility helpers

## Game Integration Pattern

### Standard Game Structure

```javascript
// Game initialization
export function initGame() {
  // Setup game canvas
  // Initialize application
  // Load game assets
  // Setup event listeners
}

// Game logic
export function gameLoop() {
  // Update game state
  // Render graphics
  // Handle user input
}

// Cleanup
export function destroyGame() {
  // Remove event listeners
  // Cleanup resources
  // Reset game state
}
```

### Asset Management

- Centralized asset loading
- Sprite sheet optimization
- Audio preloading
- Responsive image handling

## Build System

### Development

```bash
npm run dev    # Start development server
```

### Production

```bash
npm run build  # Build for production
npm run preview # Preview production build
```

### Build Features

- JavaScript obfuscation for code protection
- Asset optimization and compression
- Manifest generation for cache busting
- Multi-entry point configuration

## Security Features

### Code Protection

- Advanced JavaScript obfuscation
- String array encoding
- Control flow flattening
- Dead code injection
- Self-defending code

### Data Security

- Token-based authentication
- Encrypted URL parameters
- Secure communication protocols
- Input validation and sanitization

## Performance Optimizations

### Asset Loading

- Lazy loading of game modules
- Sprite sheet optimization
- Audio compression
- Image optimization

## Extensibility

### Adding New Games

1. Create game directory in `Games/`
2. Implement standard game structure
3. Add route configuration in `router.js`
4. Update build configuration in `vite.config.js`
5. Add game assets to appropriate directories

### Custom Features

- Plugin system for additional functionality
- Modular component architecture
- Event-driven communication
- Configurable game parameters

## Browser Compatibility

### Supported Browsers

- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### Mobile Support

- iOS Safari 13+
- Android Chrome 80+
- Responsive design
- Touch input optimization

## Development Guidelines

### Code Standards

- ES6+ module system
- Consistent naming conventions
- Comprehensive error handling
- Performance-first approach

### Testing Strategy

- Cross-browser testing
- Mobile device testing
- Performance profiling
- Security validation

This architecture provides a scalable, maintainable, and secure foundation for core development with modern web technologies.
