import Phaser from 'phaser';
import TicTapsConnector from '../utils/TicTapsConnector';
import GameConfig from '../config/GameConfig';

export default class GameStartScene extends Phaser.Scene {
  private ticTaps: TicTapsConnector;
  private startButton!: Phaser.GameObjects.Image;
  private isStarting: boolean = false;

  constructor() {
    super('GameStartScene');
    this.ticTaps = new TicTapsConnector();
  }

  create(): void {
    const { width, height } = this.cameras.main;

    // Background
    this.add.image(0, 0, 'game_bg')
        .setOrigin(0, 0)
        .setDisplaySize(width, height);

    // Game Title
    const gameTitle = this.add.image(width / 2, height * 0.25, 'game_name').setOrigin(0.5);
    const titleScale = Math.min((width * 0.7) / gameTitle.width, 0.8); // Limit max scale to 0.8
    gameTitle.setScale(titleScale);

    // Subtle pulse animation
    this.tweens.add({
      targets: gameTitle,
      scale: titleScale * 1.05,
      duration: 2000,
      yoyo: true,
      repeat: -1,
      ease: 'Sine.easeInOut'
    });

    this.createStartButton();
  }

  private createStartButton(): void {
    const { width, height } = this.cameras.main;

    // Start Button
    this.startButton = this.add.image(width / 2, height * 0.6, 'button_bg').setOrigin(0.5);
    const buttonScale = Math.min((width * 0.6) / this.startButton.width, 1.0); // Limit max scale to 1.0
    this.startButton.setScale(buttonScale);

    // Start Text
    const startText = this.add.image(this.startButton.x, this.startButton.y - 5, 'game_start').setOrigin(0.5);
    const textScale = Math.min((this.startButton.displayWidth * 0.5) / startText.width, 0.6); // Much smaller text
    startText.setScale(textScale);

    // Make interactive
    this.startButton.setInteractive({ useHandCursor: true });

    // Simple hover effect
    this.startButton.on('pointerover', () => {
      this.tweens.add({
        targets: this.startButton,
        scale: buttonScale * 1.05,
        duration: 150
      });
      this.tweens.add({
        targets: startText,
        scale: textScale * 1.05,
        duration: 150
      });
    });

    this.startButton.on('pointerout', () => {
      this.tweens.add({
        targets: this.startButton,
        scale: buttonScale,
        duration: 150
      });
      this.tweens.add({
        targets: startText,
        scale: textScale,
        duration: 150
      });
    });

    // Click handler
    this.startButton.on('pointerdown', () => {
      if (this.isStarting) return;
      this.isStarting = true;

      this.ticTaps.notifyGameReady();

      // Simple transition to game
      this.tweens.add({
        targets: this.startButton,
        scale: buttonScale * 0.95,
        duration: 100,
        yoyo: true,
        onComplete: () => this.scene.start('GameScene')
      });
      this.tweens.add({
        targets: startText,
        scale: textScale * 0.95,
        duration: 100,
        yoyo: true
      });
    });
  }

}
